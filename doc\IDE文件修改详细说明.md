# VS Code 文件修改详细说明

本文档详细说明了 AugmentCode-Free 工具对 VS Code 配置文件的修改方式和内容。

## 概述

AugmentCode-Free 工具主要修改两类文件：
1. **state.vscdb** - SQLite 数据库文件
2. **storage.json** - JSON 配置文件

## 1. state.vscdb 文件修改

### 文件描述
- **文件类型**: SQLite 数据库文件
- **作用**: 存储 IDE 的状态信息、扩展数据、用户设置等
- **位置**: `globalStorage/state.vscdb`

### 文件路径
```
Windows:
%APPDATA%\Code\User\globalStorage\state.vscdb

macOS:
~/Library/Application Support/Code/User/globalStorage/state.vscdb

Linux:
~/.config/Code/User/globalStorage/state.vscdb
```

### 数据库结构
```sql
CREATE TABLE ItemTable (
    key TEXT PRIMARY KEY,
    value BLOB
);
```

### 修改内容
**目标**: 删除所有包含 "augment" 关键字的条目

**SQL 操作**:
```sql
-- 查找包含 "augment" 的条目
SELECT key FROM ItemTable WHERE key LIKE '%augment%';

-- 删除包含 "augment" 的条目
DELETE FROM ItemTable WHERE key LIKE '%augment%';
```

### 修改示例
**修改前的数据**:
```
key: "storage.testKey1"           -> value: "testValue1"
key: "augment.testKey2"           -> value: "testValue2"
key: "another.augment.key"        -> value: "testValue3"
key: "noKeywordHere"              -> value: "testValue4"
key: "prefix.augment"             -> value: "testValue5"
```

**修改后的数据**:
```
key: "storage.testKey1"           -> value: "testValue1"
key: "noKeywordHere"              -> value: "testValue4"
```

### 安全措施
1. **自动备份**: 修改前创建 `.backup` 后缀的备份文件
2. **错误恢复**: 如果修改失败，自动从备份恢复
3. **权限检查**: 验证文件访问权限
4. **完整性验证**: 确保数据库结构完整

## 2. storage.json 文件修改

### 文件描述
- **文件类型**: JSON 配置文件
- **作用**: 存储遥测信息、机器标识符等
- **位置**: `globalStorage/storage.json`

### 文件路径
```
Windows:
%APPDATA%\Code\User\globalStorage\storage.json

macOS:
~/Library/Application Support/Code/User/globalStorage/storage.json

Linux:
~/.config/Code/User/globalStorage/storage.json
```

### 文件结构示例
```json
{
    "machineId": "original-machine-id-12345",
    "some_other_key": "value",
    "telemetry": {
        "machineId": "original-telemetry-machine-id",
        "devDeviceId": "original-device-id-67890",
        "another_telemetry_key": "value"
    }
}
```

### 修改内容
**目标**: 更新机器标识符和设备标识符

**修改的字段**:
1. **根级 machineId**: 顶层的机器标识符
2. **telemetry.machineId**: 遥测部分的机器标识符
3. **telemetry.devDeviceId**: 开发设备标识符

### ID 生成规则
```python
# 机器 ID 生成 (32位十六进制)
new_machine_id = uuid.uuid4().hex

# 设备 ID 生成 (标准 UUID 格式)
new_device_id = str(uuid.uuid4())
```

### 修改示例
**修改前**:
```json
{
    "machineId": "abc123def456ghi789jkl012mno345pq",
    "some_other_key": "value",
    "telemetry": {
        "machineId": "old-telemetry-machine-id",
        "devDeviceId": "old-device-id-12345-67890",
        "another_telemetry_key": "value"
    }
}
```

**修改后**:
```json
{
    "machineId": "f47ac10b58cc4372a5670e02b2c3d479",
    "some_other_key": "value",
    "telemetry": {
        "machineId": "f47ac10b58cc4372a5670e02b2c3d479",
        "devDeviceId": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
        "another_telemetry_key": "value"
    }
}
```

### 安全措施
1. **JSON 验证**: 确保文件格式正确
2. **备份机制**: 修改前创建备份
3. **原子操作**: 确保修改的完整性
4. **错误处理**: 修改失败时自动恢复

## 修改流程

### 通用流程
1. **检查文件存在性**
2. **创建备份文件**
3. **读取原始内容**
4. **执行修改操作**
5. **验证修改结果**
6. **保存修改内容**
7. **错误时恢复备份**

### 错误处理
- 文件不存在时的处理
- 权限不足时的提示
- 格式错误时的恢复
- 备份失败时的中止

## 注意事项

1. **IDE 必须关闭**: 修改前确保相关 IDE 完全关闭
2. **权限要求**: 需要对配置目录的读写权限
3. **备份重要性**: 所有修改都会创建备份文件
4. **版本兼容性**: 支持各 IDE 的不同版本
5. **安全性**: 只修改特定的标识符字段，不影响其他配置

## 恢复方法

如需恢复原始配置：
1. 找到对应的 `.backup` 备份文件
2. 删除当前的配置文件
3. 将备份文件重命名为原文件名
4. 重启 IDE

## 支持的 IDE

- **VS Code** (Visual Studio Code)
