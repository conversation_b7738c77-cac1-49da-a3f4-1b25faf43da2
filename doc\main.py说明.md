# main.py 代码说明文档

## 文件概述
`main.py` 是 AugmentCode-Free 工具的主启动程序，负责启动图形用户界面。这是用户双击运行或在命令行中执行 `python main.py` 时的入口点。

## 主要功能

### 1. 程序入口
- 作为整个应用程序的启动入口点
- 处理跨平台兼容性问题
- 管理Python路径和工作目录设置

### 2. 路径管理
```python
# 强化路径设置 - 确保跨平台兼容
current_dir = Path(__file__).resolve().parent
current_dir_str = str(current_dir)

# 清理并重新设置Python路径
if current_dir_str in sys.path:
    sys.path.remove(current_dir_str)
sys.path.insert(0, current_dir_str)

# 确保工作目录正确
os.chdir(current_dir)
```

### 3. 错误处理和自动修复
程序包含完善的错误处理机制：

#### ImportError 自动修复
- 当模块导入失败时，自动尝试多种路径修复方案
- 支持多个可能的路径位置
- 提供详细的故障排除建议

#### 异常处理
- 捕获并处理启动过程中的各种异常
- 提供用户友好的错误信息
- 给出具体的解决方案建议

### 4. 依赖管理
程序会导入以下关键模块：
- `config_manager`: 配置管理器
- `language_manager`: 语言管理器
- `gui_qt6.main_window`: PyQt6主窗口

## 代码结构

### main() 函数
主函数执行以下步骤：

1. **路径初始化**
   - 获取当前文件的绝对路径
   - 设置Python模块搜索路径
   - 切换到正确的工作目录

2. **模块导入**
   - 导入配置和语言管理器
   - 导入PyQt6主窗口模块
   - 处理导入失败的情况

3. **应用启动**
   - 初始化配置和语言管理器
   - 启动PyQt6图形界面
   - 返回应用程序退出代码

### 错误恢复机制
```python
# 尝试多种路径修复方案
possible_paths = [
    current_dir,
    current_dir.parent,
    Path.cwd(),
    Path(__file__).parent
]

for path in possible_paths:
    try:
        path_str = str(path.resolve())
        if path_str not in sys.path:
            sys.path.insert(0, path_str)
        # 尝试导入模块
        from config_manager import get_config_manager
        # ... 其他导入
        fixed = True
        break
    except ImportError:
        continue
```

## 平台兼容性

### 跨平台支持
- 使用 `pathlib.Path` 确保路径处理的跨平台兼容性
- 支持 Windows、macOS 和 Linux 系统
- 自动处理不同操作系统的路径分隔符

### macOS 特殊处理
- 针对 macOS 系统的模块导入问题提供特殊修复
- 支持多种可能的应用程序路径结构

## 用户交互

### 控制台输出
程序提供丰富的控制台反馈：
- 启动状态信息
- 错误诊断信息
- 解决方案建议
- 进度指示

### 错误提示
当出现问题时，程序会显示：
- 具体的错误描述
- 可能的原因分析
- 详细的解决步骤
- 替代方案建议

## 最佳实践

### 1. 模块化设计
- 将GUI逻辑分离到独立的模块中
- 使用配置管理器统一管理设置
- 通过语言管理器支持国际化

### 2. 错误处理
- 提供多层次的错误恢复机制
- 给出用户友好的错误信息
- 包含详细的故障排除指南

### 3. 跨平台兼容
- 使用标准库的跨平台功能
- 避免硬编码的路径分隔符
- 支持不同操作系统的特殊需求

## 使用方法

### 直接运行
```bash
python main.py
```

### 作为模块运行
```bash
python -m main
```

### 双击运行
在文件管理器中双击 `main.py` 文件（需要正确配置Python关联）

## 依赖要求
- Python 3.7+
- PyQt6
- pathlib (Python 3.4+ 内置)
- 项目内部模块：config_manager, language_manager, gui_qt6

## 注意事项
1. 确保所有依赖模块都在正确的位置
2. 运行前检查Python环境是否正确配置
3. 如遇到导入错误，程序会自动尝试修复
4. 建议在项目根目录下运行以确保最佳兼容性