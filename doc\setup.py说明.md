# setup.py 代码说明文档

## 文件概述
`setup.py` 是 AugmentCode-Free 项目的 Python 包配置文件，使用 setuptools 定义了项目的元数据、依赖关系和安装配置。它是将项目打包为可分发的 Python 包的核心文件。

## 主要功能

### 1. 包配置和元数据
- 定义项目的基本信息（名称、版本、作者等）
- 配置包的分类和许可证信息
- 设置项目描述和长描述

### 2. 依赖管理
- 自动解析 requirements.txt 文件
- 设置 Python 版本要求
- 管理运行时依赖

### 3. 入口点配置
- 配置命令行工具入口点
- 设置 GUI 应用程序入口点
- 提供便捷的执行方式

## 核心配置

### 1. 项目基本信息
```python
setup(
    name="augment-tools-core",
    version="1.0.6",
    author="BasicProtein",
    author_email="<EMAIL>",
    description="Core tools for VS Code maintenance, inspired by augment-vip.",
    url="https://github.com/BasicProtein/AugmentCode-Free",
)
```

#### 配置说明
- **name**: PyPI 上的包名称
- **version**: 包版本号，遵循语义化版本控制
- **author**: 项目作者
- **description**: 简短的项目描述
- **url**: 项目主页 URL

### 2. 长描述配置
```python
long_description=Path("README.md").read_text(encoding="utf-8") if Path("README.md").exists() else "",
long_description_content_type="text/markdown",
```

#### 功能特点
- 自动读取 README.md 文件作为长描述
- 支持 Markdown 格式
- 文件不存在时提供空字符串回退
- 使用 UTF-8 编码确保国际化支持

### 3. 包发现配置
```python
packages=find_packages(where=".", exclude=["tests*", ".venv*"]),
```

#### 包发现规则
- 自动发现项目中的所有 Python 包
- 排除测试包和虚拟环境
- 包含 `augment_tools_core` 主包

### 4. 依赖解析函数
```python
def parse_requirements(filename="requirements.txt"):
    """从 pip requirements 文件加载依赖"""
    try:
        with open(Path(__file__).parent / filename, "r") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except FileNotFoundError:
        print(f"Warning: '{filename}' not found. No dependencies will be installed.")
        return []
```

#### 功能特性
- 自动解析 requirements.txt 文件
- 过滤空行和注释行
- 文件不存在时提供警告和空列表回退
- 支持相对路径解析

### 5. Python 版本要求
```python
python_requires=">=3.7",
```

#### 版本选择理由
- Python 3.7+ 支持 f-string 语法
- pathlib 模块的完整支持
- 现代 Python 特性的可用性
- 与项目代码的兼容性

### 6. 入口点配置
```python
entry_points={
    "console_scripts": [
        "augment-tools = augment_tools_core.cli:main_cli",
        "augment-tools-gui = main:main",
    ],
},
```

#### 入口点说明
- **augment-tools**: 命令行工具入口点
- **augment-tools-gui**: GUI 应用程序入口点
- 安装后可直接在命令行中使用

### 7. 分类器配置
```python
classifiers=[
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Operating System :: OS Independent",
],
```

#### 分类器作用
- **开发状态**: 标识为生产稳定版本
- **目标用户**: 面向开发者
- **许可证**: MIT 开源许可证
- **Python 版本**: 支持的 Python 版本列表
- **操作系统**: 跨平台兼容

### 8. 项目链接
```python
project_urls={
    'Bug Reports': 'https://github.com/BasicProtein/AugmentCode-Free/issues',
    'Source': 'https://github.com/BasicProtein/AugmentCode-Free',
},
```

#### 链接类型
- **Bug Reports**: 问题报告链接
- **Source**: 源代码仓库链接
- 便于用户找到相关资源

## 安装和使用

### 1. 开发安装
```bash
# 克隆仓库
git clone https://github.com/BasicProtein/AugmentCode-Free.git
cd AugmentCode-Free

# 开发模式安装
pip install -e .
```

### 2. 生产安装
```bash
# 从源码安装
pip install .

# 从 PyPI 安装（如果已发布）
pip install augment-tools-core
```

### 3. 依赖安装
```bash
# 手动安装依赖
pip install -r requirements.txt

# 通过 setup.py 自动安装
pip install .
```

## 构建和分发

### 1. 构建包
```bash
# 安装构建工具
pip install build

# 构建 wheel 和 sdist
python -m build

# 输出文件
# dist/augment_tools_core-1.0.6-py3-none-any.whl
# dist/augment-tools-core-1.0.6.tar.gz
```

### 2. 上传到 PyPI
```bash
# 安装上传工具
pip install twine

# 上传到测试 PyPI
twine upload --repository testpypi dist/*

# 上传到正式 PyPI
twine upload dist/*
```

### 3. 验证安装
```bash
# 验证命令行工具
augment-tools --help

# 验证 GUI 工具
augment-tools-gui
```

## 配置最佳实践

### 1. 版本管理
- 使用语义化版本控制（SemVer）
- 在代码中保持版本号一致性
- 考虑使用版本管理工具

### 2. 依赖管理
- 明确指定依赖版本范围
- 定期更新依赖版本
- 测试不同依赖版本的兼容性

### 3. 元数据维护
- 保持项目描述的准确性
- 及时更新作者和联系信息
- 确保分类器的正确性

## 常见问题和解决方案

### 1. 依赖解析失败
```python
# 问题：requirements.txt 文件不存在
# 解决：parse_requirements 函数提供了回退机制

def parse_requirements(filename="requirements.txt"):
    try:
        # 尝试读取文件
        with open(Path(__file__).parent / filename, "r") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except FileNotFoundError:
        # 文件不存在时的处理
        print(f"Warning: '{filename}' not found. No dependencies will be installed.")
        return []
```

### 2. 包发现问题
```python
# 确保包结构正确
augment_tools_core/
├── __init__.py  # 必须存在
├── cli.py
├── common_utils.py
└── ...
```

### 3. 入口点不工作
```bash
# 重新安装包
pip uninstall augment-tools-core
pip install .

# 或者使用开发模式
pip install -e .
```

## 扩展和定制

### 1. 添加新的入口点
```python
entry_points={
    "console_scripts": [
        "augment-tools = augment_tools_core.cli:main_cli",
        "augment-tools-gui = main:main",
        "augment-tools-debug = augment_tools_core.debug:main",  # 新增
    ],
},
```

### 2. 添加可选依赖
```python
extras_require={
    "dev": ["pytest", "black", "flake8"],
    "gui": ["PyQt6"],
    "all": ["pytest", "black", "flake8", "PyQt6"],
},
```

### 3. 包含数据文件
```python
package_data={
    "augment_tools_core": ["data/*.json", "templates/*.txt"],
},
# 或者
include_package_data=True,
```

## 与其他文件的关系

### 1. requirements.txt
- setup.py 自动读取 requirements.txt
- 保持两个文件的依赖一致性
- requirements.txt 用于开发环境

### 2. README.md
- 自动作为包的长描述
- 在 PyPI 上显示项目信息
- 支持 Markdown 格式

### 3. LICENSE
- 与 setup.py 中的许可证信息对应
- 提供法律保护
- 影响包的使用条款

## 注意事项

1. **版本一致性**: 确保 setup.py 中的版本号与项目其他地方保持一致
2. **依赖版本**: 合理设置依赖版本范围，避免过于严格或过于宽松
3. **Python 版本**: 根据代码实际使用的特性设置最低 Python 版本要求
4. **包名唯一性**: 确保包名在 PyPI 上是唯一的
5. **元数据准确性**: 保持所有元数据信息的准确性和时效性

这个 setup.py 文件提供了完整的包配置，支持项目的打包、分发和安装，是 Python 项目标准化的重要组成部分。