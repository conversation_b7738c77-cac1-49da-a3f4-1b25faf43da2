# augment_tools_core 模块说明文档

## 模块概述
`augment_tools_core` 是 AugmentCode-Free 的核心功能模块，包含了所有主要的IDE维护和管理功能。该模块采用模块化设计，将不同的功能分离到独立的子模块中，便于维护和扩展。

## 模块结构

```
augment_tools_core/
├── __init__.py              # 模块初始化文件（空）
├── cli.py                   # 命令行接口
├── common_utils.py          # 通用工具函数
├── database_manager.py      # 数据库管理功能
├── telemetry_manager.py     # 遥测ID管理功能
└── jetbrains_manager.py     # JetBrains产品管理功能
```

## 核心功能模块

### 1. common_utils.py - 通用工具模块

#### 主要功能
- **IDE类型定义**：定义支持的IDE类型枚举
- **路径管理**：跨平台的IDE配置路径检测
- **控制台输出**：彩色的控制台消息输出
- **ID生成**：生成新的机器ID和设备ID
- **文件备份**：创建配置文件的安全备份

#### 核心类和函数
```python
class IDEType(Enum):
    """支持的IDE类型"""
    VSCODE = "vscode"
    CURSOR = "cursor"
    WINDSURF = "windsurf"
    JETBRAINS = "jetbrains"

def get_ide_paths(ide_type: IDEType) -> Optional[Dict[str, Path]]:
    """获取指定IDE的配置文件路径"""

def print_info(message: str) -> None:
    """打印信息消息（蓝色）"""

def generate_new_machine_id() -> str:
    """生成新的64字符十六进制机器ID"""
```

#### 跨平台路径支持
- **Windows**: `%APPDATA%/[IDE]/User/globalStorage/`
- **macOS**: `~/Library/Application Support/[IDE]/User/globalStorage/`
- **Linux**: `~/.config/[IDE]/User/globalStorage/`

### 2. database_manager.py - 数据库管理模块

#### 主要功能
- **SQLite数据库清理**：清理IDE状态数据库中的特定条目
- **关键字搜索**：基于关键字的条目查找和删除
- **备份机制**：操作前自动创建数据库备份
- **多IDE支持**：支持VS Code、Cursor、Windsurf

#### 核心函数
```python
def clean_ide_database(ide_type: IDEType, keyword: str = "augment") -> bool:
    """清理指定IDE的SQLite数据库"""

def clean_vscode_database(db_path: Path, keyword: str = "augment") -> bool:
    """清理SQLite数据库中包含特定关键字的条目"""
```

#### 数据库操作流程
1. **路径验证**：检查数据库文件是否存在
2. **备份创建**：创建原始数据库的备份
3. **连接数据库**：建立SQLite连接
4. **查询条目**：搜索包含关键字的条目
5. **删除操作**：删除匹配的条目
6. **结果验证**：验证删除操作的结果

### 3. telemetry_manager.py - 遥测管理模块

#### 主要功能
- **遥测ID修改**：修改IDE的机器ID和设备ID
- **JSON配置处理**：读取和修改storage.json文件
- **多层级ID支持**：处理根级和遥测级的ID
- **JetBrains集成**：支持JetBrains产品的SessionID修改
- **自动备份机制**：操作前自动创建配置文件备份
- **错误恢复**：操作失败时自动从备份恢复
- **详细诊断**：提供故障排除建议和目录结构分析

#### 核心函数

##### 主入口函数
```python
def modify_ide_telemetry_ids(ide_type: IDEType) -> bool:
    """修改指定IDE的遥测ID"""
```
- **功能**：根据IDE类型选择相应的处理方式
- **JetBrains特殊处理**：调用专门的JetBrains SessionID修改函数
- **路径验证**：检查IDE配置路径的有效性
- **统一接口**：为所有IDE类型提供统一的调用接口

##### VS Code系列处理函数
```python
def modify_vscode_telemetry_ids(storage_json_path: Path) -> bool:
    """修改storage.json中的遥测ID"""
```

#### 详细功能实现

##### 1. 文件存在性检查
- **路径验证**：检查storage.json文件是否存在
- **父目录分析**：当文件不存在时，分析父目录结构
- **权限检查**：检测目录访问权限
- **故障排除建议**：
  - 确保IDE已正确安装并至少运行过一次
  - 检查IDE是否已完全关闭
  - 验证用户权限是否足够访问配置目录

##### 2. 目录结构诊断
```python
# 检查父目录是否存在
parent_dir = storage_json_path.parent
if parent_dir.exists():
    print_info(f"父目录存在: {parent_dir}")
    try:
        files_in_parent = list(parent_dir.iterdir())
        if files_in_parent:
            print_info("父目录中的文件:")
            for file in files_in_parent[:10]:  # 只显示前10个文件
                print_info(f"  - {file.name}")
            if len(files_in_parent) > 10:
                print_info(f"  ... 还有 {len(files_in_parent) - 10} 个文件")
```
- **目录内容列举**：显示配置目录中的文件列表
- **文件数量限制**：避免输出过多文件信息
- **权限错误处理**：优雅处理权限不足的情况

##### 3. ID生成和修改
- **新ID生成**：
  ```python
  new_machine_id = generate_new_machine_id()
  new_device_id = generate_new_device_id()
  ```
- **多层级ID处理**：
  - **根级machineId**：直接在JSON根级的machineId字段
  - **遥测级machineId**：在telemetry对象内的machineId字段
  - **设备ID**：在telemetry对象内的devDeviceId字段

##### 4. JSON数据处理
```python
# 读取JSON文件
with open(storage_json_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

# 修改ID字段
if 'machineId' in data:
    if data['machineId'] != new_machine_id:
        data['machineId'] = new_machine_id
        modified = True

if 'telemetry' in data and isinstance(data['telemetry'], dict):
    if 'machineId' in data['telemetry']:
        data['telemetry']['machineId'] = new_machine_id
    if 'devDeviceId' in data['telemetry']:
        data['telemetry']['devDeviceId'] = new_device_id
```

##### 5. 智能修改检测
- **变更检测**：只有当新ID与现有ID不同时才进行修改
- **修改标记**：跟踪是否实际进行了修改操作
- **无变更处理**：当ID已经匹配新值时，避免不必要的文件写入

##### 6. 错误处理和恢复
```python
except json.JSONDecodeError as e:
    print_error(f"处理 {storage_json_path} 时 JSON 解码错误: {e}")
except IOError as e:
    print_error(f"处理 {storage_json_path} 时 IO 错误: {e}")
except Exception as e:
    print_error(f"遥测 ID 修改过程中发生意外错误: {e}")

# 错误恢复机制
if backup_path and backup_path.exists():
    print_info(f"尝试从备份恢复存储文件: {backup_path}")
    try:
        shutil.copy2(backup_path, storage_json_path)
        print_success("存储文件已从备份成功恢复。")
    except Exception as restore_e:
        print_error(f"从备份恢复存储文件失败: {restore_e}")
```

#### 内置测试功能

##### 测试数据结构
```python
original_content = {
    "machineId": "old_machine_id_root",
    "some_other_key": "value",
    "telemetry": {
        "machineId": "old_machine_id_telemetry",
        "devDeviceId": "old_dev_device_id",
        "another_telemetry_key": "value"
    }
}
```

##### 测试验证流程
1. **创建测试文件**：生成包含测试数据的storage.json
2. **执行修改操作**：调用遥测ID修改函数
3. **结果验证**：
   - 检查根级machineId是否已更新
   - 检查遥测级machineId是否已更新
   - 检查devDeviceId是否已更新
4. **清理操作**：删除测试文件和备份文件

##### 验证逻辑
```python
if new_machine_id_root == original_machine_id_root:
    print_error("Verification failed: Root machineId was not updated.")
if new_machine_id_telemetry == original_machine_id_telemetry:
    print_error("Verification failed: telemetry.machineId was not updated.")
if new_dev_device_id == original_dev_device_id:
    print_error("Verification failed: telemetry.devDeviceId was not updated.")
```

#### 支持的ID类型
- **machineId（根级）**：IDE实例的主要机器标识符
- **machineId（遥测级）**：遥测系统使用的机器标识符
- **devDeviceId**：开发设备的唯一标识符
- **SessionID（JetBrains）**：JetBrains产品的会话标识符

#### 错误处理策略
1. **分层错误处理**：针对不同类型的错误采用不同的处理策略
2. **用户友好提示**：提供清晰的错误信息和解决建议
3. **自动恢复机制**：操作失败时自动从备份恢复原始文件
4. **权限问题处理**：优雅处理文件访问权限不足的情况
5. **JSON格式错误**：处理配置文件格式损坏的情况

#### 安全特性
- **操作前备份**：使用`create_backup()`函数创建安全备份
- **原子性操作**：确保文件修改的原子性
- **权限验证**：检查文件访问权限
- **数据完整性**：验证JSON格式的正确性
- **回滚机制**：失败时自动恢复到原始状态

### 4. jetbrains_manager.py - JetBrains管理模块

#### 主要功能
- **产品检测**：自动检测已安装的JetBrains产品
- **SessionID管理**：修改JetBrains产品的SessionID
- **XML配置处理**：处理ide.general.xml配置文件
- **用户设置保护**：保护用户的字体和个人设置

#### 支持的产品
```python
def get_jetbrains_products() -> List[str]:
    return [
        "PyCharm", "IntelliJIdea", "WebStorm", "PhpStorm",
        "CLion", "DataGrip", "GoLand", "RubyMine",
        "AppCode", "AndroidStudio", "Rider", "DataSpell"
    ]
```

#### 核心函数
```python
def find_jetbrains_installations() -> Dict[str, List[Path]]:
    """查找已安装的JetBrains产品"""

def modify_jetbrains_session_id(config_dir: Path, session_id: str) -> bool:
    """修改指定JetBrains产品的SessionID"""

def modify_all_jetbrains_session_ids(custom_session_id: Optional[str] = None) -> bool:
    """修改所有JetBrains产品的SessionID"""
```

### 5. cli.py - 命令行接口模块

#### 主要功能
- **命令行参数解析**：使用Click框架处理命令行参数
- **多IDE支持**：统一的命令行接口支持所有IDE
- **向后兼容**：保持与旧版本命令的兼容性
- **国际化支持**：支持多语言的命令行界面

#### 主要命令
```python
@main_cli.command("clean-db")
def clean_db_command(ide: str, keyword: str):
    """清理指定IDE的状态数据库"""

@main_cli.command("modify-ids")
def modify_ids_command(ide: str):
    """修改指定IDE的遥测ID"""

@main_cli.command("run-all")
def run_all_command(ctx, ide: str, keyword: str):
    """运行所有可用工具"""
```

## 设计模式和架构

### 1. 模块化设计
- **单一职责**：每个模块负责特定的功能领域
- **松耦合**：模块间通过明确的接口交互
- **高内聚**：相关功能集中在同一模块中

### 2. 错误处理策略
- **分层错误处理**：在不同层级处理不同类型的错误
- **用户友好消息**：提供清晰的错误信息和解决建议
- **优雅降级**：部分功能失败时不影响其他功能

### 3. 跨平台兼容性
- **路径抽象**：使用pathlib处理路径操作
- **平台检测**：根据操作系统调整行为
- **编码处理**：正确处理不同平台的文本编码

## 数据流和交互

### 1. 典型工作流程
```
用户输入 → CLI解析 → IDE类型识别 → 路径检测 → 
备份创建 → 数据修改 → 结果验证 → 用户反馈
```

### 2. 模块间交互
```
cli.py → common_utils.py (路径获取)
      → database_manager.py (数据库清理)
      → telemetry_manager.py (ID修改)
      → jetbrains_manager.py (JetBrains处理)
```

### 3. 错误传播
- **布尔返回值**：成功/失败的简单指示
- **异常处理**：在适当的层级捕获和处理异常
- **日志记录**：详细记录操作过程和错误信息

## 配置和扩展

### 1. 添加新IDE支持
1. 在`IDEType`枚举中添加新类型
2. 在`get_ide_paths()`中添加路径逻辑
3. 在`get_ide_process_names()`中添加进程名称
4. 测试新IDE的所有功能

### 2. 扩展功能模块
1. 创建新的功能模块文件
2. 实现标准的函数接口
3. 在CLI模块中添加相应命令
4. 更新文档和测试

### 3. 自定义配置
- **关键字定制**：支持自定义清理关键字
- **路径覆盖**：支持手动指定配置路径
- **行为控制**：通过参数控制具体行为

## 安全考虑

### 1. 数据保护
- **自动备份**：修改前自动创建备份
- **权限检查**：验证文件访问权限
- **原子操作**：确保操作的原子性

### 2. 输入验证
- **路径验证**：验证文件路径的有效性
- **参数检查**：验证输入参数的合法性
- **类型检查**：确保数据类型的正确性

### 3. 错误恢复
- **备份恢复**：操作失败时自动恢复备份
- **状态检查**：验证操作后的系统状态
- **清理机制**：清理临时文件和资源

## 性能优化

### 1. 缓存机制
- **进程缓存**：缓存进程检测结果
- **路径缓存**：缓存路径检测结果
- **配置缓存**：缓存配置文件内容

### 2. 并发处理
- **异步操作**：支持异步的文件操作
- **批量处理**：支持批量处理多个IDE
- **进度反馈**：提供操作进度信息

### 3. 资源管理
- **内存优化**：及时释放不需要的资源
- **文件句柄**：正确管理文件句柄
- **临时文件**：及时清理临时文件

## 测试和调试

### 1. 单元测试
- 每个模块都包含内置的测试代码
- 使用虚拟数据进行安全测试
- 覆盖主要的功能路径

### 2. 集成测试
- 测试模块间的交互
- 验证端到端的工作流程
- 测试错误处理机制

### 3. 调试支持
- 详细的日志输出
- 调试模式支持
- 错误堆栈跟踪

## 使用示例

### 程序化使用
```python
from augment_tools_core.common_utils import IDEType
from augment_tools_core.database_manager import clean_ide_database
from augment_tools_core.telemetry_manager import modify_ide_telemetry_ids

# 清理VS Code数据库
success = clean_ide_database(IDEType.VSCODE, "augment")

# 修改Cursor遥测ID
success = modify_ide_telemetry_ids(IDEType.CURSOR)
```

### 命令行使用
```bash
# 清理数据库
augment-tools clean-db --ide vscode --keyword augment

# 修改遥测ID
augment-tools modify-ids --ide cursor

# 运行所有工具
augment-tools run-all --ide windsurf
```

这个核心模块提供了完整的IDE维护功能，支持多种IDE类型，具有良好的扩展性和跨平台兼容性。


