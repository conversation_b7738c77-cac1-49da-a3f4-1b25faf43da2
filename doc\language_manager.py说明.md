# language_manager.py 代码说明文档

## 文件概述
`language_manager.py` 是 AugmentCode-Free 的国际化和语言管理模块，负责处理多语言支持、语言切换功能以及文本本地化。它提供了完整的i18n（国际化）解决方案。

## 主要功能

### 1. 多语言支持
- 支持中文（简体）和英文两种语言
- 动态语言切换功能
- 文本本地化和格式化
- 语言配置持久化

### 2. 文本管理
- 基于键路径的文本检索系统
- 支持文本参数化和格式化
- 自动回退到英文（当当前语言缺少翻译时）
- 嵌套JSON结构的文本组织

## 核心类：LanguageManager

### 初始化
```python
def __init__(self, config_manager=None):
    self.config_manager = config_manager
    self.current_language = "zh_CN"  # 默认语言
    self.languages = {}
    self.available_languages = {
        "zh_CN": "简体中文",
        "en_US": "English"
    }
```

### 支持的语言
- **zh_CN**: 简体中文（默认）
- **en_US**: English

## 主要方法

### 1. 语言文件加载
```python
def _load_languages(self):
    """加载所有可用的语言文件"""
    # 使用绝对路径确保跨平台兼容性
    languages_dir = Path(__file__).resolve().parent / "languages"
    
    for lang_code in self.available_languages.keys():
        lang_file = languages_dir / f"{lang_code}.json"
        if lang_file.exists():
            try:
                with open(lang_file, 'r', encoding='utf-8') as f:
                    self.languages[lang_code] = json.load(f)
            except Exception as e:
                print(f"Error loading language file {lang_file}: {e}")
                self.languages[lang_code] = {}
```

### 2. 语言设置
```python
def set_language(self, language_code: str):
    """设置当前语言"""
    if language_code in self.available_languages:
        self.current_language = language_code
        
        # 保存到配置文件
        if self.config_manager:
            self.config_manager.set_setting("language", language_code)
    else:
        print(f"Unsupported language: {language_code}")
```

### 3. 文本检索系统
```python
def get_text(self, key_path: str, **kwargs) -> str:
    """
    通过键路径获取翻译文本
    
    Args:
        key_path: 点分隔的路径 (例如: 'buttons.ok')
        **kwargs: 字符串格式化的变量
        
    Returns:
        翻译后的文本，如果未找到则返回key_path
    """
    try:
        # 获取当前语言数据
        lang_data = self.languages.get(self.current_language, {})
        
        # 通过键路径导航嵌套字典
        keys = key_path.split('.')
        value = lang_data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                # 回退到英文
                if self.current_language != "en_US":
                    return self._get_fallback_text(key_path, **kwargs)
                else:
                    return key_path
        
        # 使用提供的kwargs格式化字符串
        if isinstance(value, str) and kwargs:
            try:
                return value.format(**kwargs)
            except KeyError as e:
                print(f"Missing format key {e} for text: {value}")
                return value
        
        return str(value)
        
    except Exception as e:
        print(f"Error getting text for key '{key_path}': {e}")
        return key_path
```

### 4. 回退机制
```python
def _get_fallback_text(self, key_path: str, **kwargs) -> str:
    """从英文获取回退文本"""
    try:
        lang_data = self.languages.get("en_US", {})
        keys = key_path.split('.')
        value = lang_data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return key_path
        
        if isinstance(value, str) and kwargs:
            try:
                return value.format(**kwargs)
            except KeyError:
                return value
        
        return str(value)
        
    except Exception:
        return key_path
```

## 全局函数接口

### 便捷函数
```python
def get_text(key_path: str, **kwargs) -> str:
    """便捷函数：获取翻译文本"""
    return get_language_manager().get_text(key_path, **kwargs)

def set_language(language_code: str):
    """便捷函数：设置语言"""
    get_language_manager().set_language(language_code)

def get_current_language() -> str:
    """便捷函数：获取当前语言"""
    return get_language_manager().get_language()
```

### 单例模式
```python
# 全局语言管理器实例
_language_manager = None

def get_language_manager(config_manager=None):
    """获取全局语言管理器实例"""
    global _language_manager
    if _language_manager is None:
        _language_manager = LanguageManager(config_manager)
    return _language_manager
```

## 语言文件结构

### JSON 文件组织
语言文件使用嵌套的JSON结构组织文本：

```json
{
  "app": {
    "title": "AugmentCode-Free",
    "welcome": "欢迎使用",
    "version": "v1.0.6 - 多IDE支持版本"
  },
  "buttons": {
    "run_all": "一键修改所有配置",
    "close_ide": "关闭选中的IDE",
    "clean_db": "清理IDE数据库"
  },
  "dialogs": {
    "titles": {
      "close_confirm": "关闭{ide_name}确认"
    },
    "messages": {
      "welcome_message": "欢迎使用 AugmentCode-Free！"
    }
  }
}
```

### 键路径系统
- 使用点号分隔的路径访问嵌套文本
- 例如：`"app.title"` → `"AugmentCode-Free"`
- 例如：`"buttons.run_all"` → `"一键修改所有配置"`

## 文本格式化

### 参数化文本
支持使用Python字符串格式化语法：

```python
# 语言文件中
"close_confirm": "关闭{ide_name}确认"

# 代码中使用
text = get_text("dialogs.titles.close_confirm", ide_name="VS Code")
# 结果: "关闭VS Code确认"
```

### 格式化示例
```python
# 简单文本
title = get_text("app.title")  # "AugmentCode-Free"

# 带参数的文本
message = get_text("dialogs.messages.run_all_warning", ide_name="Cursor")

# 多参数文本
status = get_text("cli.step", step="1", operation="清理数据库")
```

## 错误处理

### 1. 文件加载错误
- 捕获JSON解析错误
- 处理文件不存在的情况
- 提供空字典作为回退

### 2. 文本检索错误
- 键路径不存在时的处理
- 自动回退到英文版本
- 格式化参数缺失的处理

### 3. 语言切换错误
- 不支持的语言代码处理
- 保持当前语言不变
- 提供错误信息反馈

## 最佳实践

### 1. 文本组织
- 按功能模块组织文本键
- 使用有意义的键名
- 保持键路径的一致性

### 2. 参数化设计
- 对于动态内容使用参数化
- 提供有意义的参数名
- 处理参数缺失的情况

### 3. 回退策略
- 英文作为默认回退语言
- 确保所有文本都有英文版本
- 优雅处理翻译缺失

## 使用示例

### 基本使用
```python
from language_manager import get_text, set_language

# 获取文本
title = get_text("app.title")
welcome = get_text("app.welcome")

# 切换语言
set_language("en_US")
title_en = get_text("app.title")  # "AugmentCode-Free"

# 参数化文本
confirm_msg = get_text("dialogs.titles.close_confirm", ide_name="VS Code")
```

### 在GUI中使用
```python
class MainWindow:
    def __init__(self, config_manager):
        self.language_manager = get_language_manager(config_manager)
        self.setup_ui()
    
    def setup_ui(self):
        # 使用本地化文本
        self.setWindowTitle(get_text("app.title"))
        self.welcome_label.setText(get_text("app.welcome"))
    
    def on_language_change(self, lang_code):
        set_language(lang_code)
        self.update_ui_texts()
```

### 添加新语言
1. 在 `languages/` 目录下创建新的JSON文件（如 `fr_FR.json`）
2. 在 `available_languages` 字典中添加语言映射
3. 确保所有必要的文本键都有翻译

## 注意事项
1. 语言文件必须使用UTF-8编码
2. JSON文件格式必须正确
3. 新增文本键时需要在所有语言文件中添加
4. 参数化文本的参数名必须在所有语言版本中保持一致
5. 建议定期检查翻译的完整性和准确性