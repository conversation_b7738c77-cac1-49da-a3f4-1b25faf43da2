# config_manager.py 代码说明文档

## 文件概述
`config_manager.py` 是 AugmentCode-Free 的配置管理模块，负责处理用户设置和配置数据的持久化存储。它提供了一个统一的接口来管理应用程序的各种配置选项。

## 主要功能

### 1. 配置文件管理
- 自动创建和管理配置目录
- 处理配置文件的读取和写入
- 提供默认配置值
- 支持配置的动态更新

### 2. 跨平台兼容性
- 使用绝对路径确保跨平台兼容性
- 自动处理目录权限问题
- 支持 Windows、macOS 和 Linux

## 核心类：ConfigManager

### 初始化
```python
def __init__(self):
    # 使用绝对路径确保跨平台兼容性
    self.config_dir = Path(__file__).resolve().parent / "config"
    self.config_file = self.config_dir / "settings.json"
    self.settings = {}
```

### 默认配置
```python
self.default_settings = {
    "language": "zh_CN",           # 默认语言
    "first_run": True,             # 首次运行标记
    "window_geometry": "520x780",   # 窗口几何信息
    "last_selected_ide": "VS Code", # 上次选择的IDE
    "show_welcome": True,          # 是否显示欢迎页面
    "show_about_on_startup": True, # 启动时显示关于对话框
    "theme": "default"             # 主题设置
}
```

## 主要方法

### 1. 配置目录管理
```python
def _ensure_config_dir(self):
    """确保配置目录存在"""
    try:
        self.config_dir.mkdir(exist_ok=True, parents=True)
        # 在macOS上确保目录权限正确
        if os.name == 'posix':
            os.chmod(self.config_dir, 0o755)
    except Exception as e:
        print(f"Error creating config directory: {e}")
```

### 2. 配置加载和保存
```python
def _load_settings(self):
    """从文件加载设置"""
    try:
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                loaded_settings = json.load(f)
                # 合并默认设置以确保所有键都存在
                self.settings = {**self.default_settings, **loaded_settings}
        else:
            # 首次运行使用默认设置
            self.settings = self.default_settings.copy()
            self._save_settings()
    except Exception as e:
        print(f"Error loading settings: {e}")
        self.settings = self.default_settings.copy()

def _save_settings(self):
    """保存设置到文件"""
    try:
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.settings, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving settings: {e}")
```

### 3. 通用设置操作
```python
def get_setting(self, key: str, default: Any = None) -> Any:
    """获取设置值"""
    return self.settings.get(key, default)

def set_setting(self, key: str, value: Any):
    """设置值并自动保存"""
    self.settings[key] = value
    self._save_settings()
```

## 专用设置方法

### 1. 首次运行管理
```python
def is_first_run(self) -> bool:
    """检查是否为首次运行"""
    return self.get_setting("first_run", True)

def mark_first_run_complete(self):
    """标记首次运行完成"""
    self.set_setting("first_run", False)
```

### 2. 欢迎页面控制
```python
def should_show_welcome(self) -> bool:
    """检查是否应显示欢迎对话框"""
    return self.get_setting("show_welcome", True)

def set_show_welcome(self, show: bool):
    """设置是否显示欢迎对话框"""
    self.set_setting("show_welcome", show)
```

### 3. 窗口几何管理
```python
def get_window_geometry(self) -> str:
    """获取窗口几何信息"""
    return self.get_setting("window_geometry", "420x680")

def set_window_geometry(self, geometry: str):
    """设置窗口几何信息"""
    self.set_setting("window_geometry", geometry)
```

### 4. IDE选择记忆
```python
def get_last_selected_ide(self) -> str:
    """获取上次选择的IDE"""
    return self.get_setting("last_selected_ide", "VS Code")

def set_last_selected_ide(self, ide: str):
    """设置上次选择的IDE"""
    self.set_setting("last_selected_ide", ide)
```

### 5. 语言设置
```python
def get_language(self) -> str:
    """获取当前语言"""
    return self.get_setting("language", "zh_CN")

def set_language(self, language: str):
    """设置当前语言"""
    self.set_setting("language", language)
```

## 全局实例管理

### 单例模式实现
```python
# 全局配置管理器实例
_config_manager = None

def get_config_manager():
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
```

## 配置文件结构

### settings.json 示例
```json
{
  "language": "zh_CN",
  "first_run": false,
  "window_geometry": "520x780",
  "last_selected_ide": "VS Code",
  "show_welcome": false,
  "show_about_on_startup": true,
  "theme": "default"
}
```

## 错误处理

### 1. 目录创建失败
- 捕获权限错误
- 提供详细的错误信息
- 在macOS上自动设置正确的目录权限

### 2. 文件读写错误
- JSON解析错误处理
- 文件不存在时的默认值处理
- 编码问题的处理

### 3. 配置恢复
- 当配置文件损坏时自动使用默认设置
- 保持应用程序的稳定运行
- 自动重新创建配置文件

## 最佳实践

### 1. 数据持久化
- 每次设置更改都立即保存到文件
- 使用UTF-8编码确保国际化支持
- JSON格式便于人工编辑和调试

### 2. 默认值管理
- 为所有配置项提供合理的默认值
- 新增配置项时自动合并到现有配置
- 向后兼容性保证

### 3. 错误恢复
- 优雅处理各种异常情况
- 提供有意义的错误信息
- 确保应用程序不会因配置问题崩溃

## 使用示例

### 基本使用
```python
from config_manager import get_config_manager

# 获取配置管理器实例
config = get_config_manager()

# 读取设置
language = config.get_language()
is_first = config.is_first_run()

# 修改设置
config.set_language("en_US")
config.mark_first_run_complete()

# 自定义设置
config.set_setting("custom_key", "custom_value")
value = config.get_setting("custom_key", "default")
```

### 与其他模块集成
```python
# 在语言管理器中使用
language_manager = get_language_manager(config_manager)

# 在GUI中使用
main_window = MainWindow(config_manager)
```

## 注意事项
1. 配置文件位于项目的 `config/` 目录下
2. 所有设置更改都会立即保存到磁盘
3. 支持自定义配置项的添加
4. 配置文件使用JSON格式，便于手动编辑
5. 在多线程环境中使用时需要注意线程安全