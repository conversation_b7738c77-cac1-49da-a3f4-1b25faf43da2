# gui_qt6 模块说明文档

## 模块概述
`gui_qt6` 是 AugmentCode-Free 的现代化图形用户界面模块，使用 PyQt6 框架实现。该模块替代了原有的 Tkinter 实现，提供了更现代、更美观、更稳定的用户界面体验。

## 模块结构

```
gui_qt6/
├── __init__.py          # 模块初始化文件
├── main_window.py       # 主窗口和应用程序入口
├── main_page.py         # 主功能页面
├── welcome_page.py      # 欢迎页面
├── about_dialog.py      # 关于对话框
├── components.py        # 通用UI组件
├── styles.py           # 样式表定义
├── font_manager.py     # 跨平台字体管理
└── workers.py          # 后台工作线程
```

## 核心组件详解

### 1. main_window.py - 主窗口模块

#### MainWindow 类
```python
class MainWindow(QMainWindow):
    """PyQt6主窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化管理器
        self.config_manager = get_config_manager()
        self.language_manager = get_language_manager(self.config_manager)
        self.font_manager = get_font_manager()
        
        # 页面堆栈
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
```

#### 主要功能
- **页面管理**：使用QStackedWidget管理多个页面
- **窗口设置**：自动居中、固定大小、标题设置
- **样式应用**：统一的样式表应用
- **生命周期管理**：正确处理窗口关闭事件

#### AugmentCodeApp 类
```python
class AugmentCodeApp:
    """AugmentCode-Free PyQt6应用程序"""
    
    def run(self):
        """运行应用程序"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            
            # 设置应用程序属性
            self.app.setApplicationName("AugmentCode-Free")
            self.app.setApplicationVersion("1.0.6")
            
            # 启用高DPI支持
            self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
```

### 2. main_page.py - 主功能页面

#### MainPage 类
```python
class MainPage(QWidget):
    """PyQt6主功能页面"""
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.language_manager = get_language_manager(config_manager)
        
        # 当前工作线程
        self.current_worker = None
        
        # 进程检查缓存
        self._process_cache = {}
```

#### UI 布局结构
1. **顶部栏**：语言选择和关于按钮
2. **标题区域**：应用标题和欢迎信息
3. **IDE选择区域**：下拉框选择目标IDE
4. **按钮区域**：主要功能按钮
5. **日志区域**：操作日志显示
6. **状态区域**：当前状态显示
7. **底部信息**：版权和链接信息

#### 主要功能按钮
```python
# 一键修改按钮
self.run_all_btn = ModernButton(get_text("buttons.run_all"), "primary")

# 关闭IDE按钮
self.close_ide_btn = ModernButton(get_text("buttons.close_ide"), "warning")

# 清理数据库按钮
self.clean_db_btn = ModernButton(get_text("buttons.clean_db"), "secondary")

# 修改遥测ID按钮
self.modify_ids_btn = ModernButton(get_text("buttons.modify_ids"), "secondary")
```

### 3. welcome_page.py - 欢迎页面

#### WelcomePage 类
```python
class WelcomePage(QWidget):
    """PyQt6欢迎页面"""
    
    continue_clicked = pyqtSignal()
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.language_manager = get_language_manager(config_manager)
```

#### 页面组件
- **标题区域**：应用名称和欢迎标题
- **语言选择**：实时语言切换功能
- **欢迎信息**：应用介绍和功能说明
- **警告信息**：重要提示和防诈骗警告
- **GitHub链接**：项目链接
- **继续按钮**：完成欢迎流程

### 4. about_dialog.py - 关于对话框

#### AboutDialog 类
```python
class AboutDialog(QDialog):
    """PyQt6关于对话框"""
    
    def __init__(self, parent=None, config_manager=None, show_dont_show_again=False):
        super().__init__(parent)
        self.config_manager = config_manager
        self.show_dont_show_again = show_dont_show_again
```

#### 对话框内容
- **应用信息**：名称、版本、描述
- **功能特性**：支持的IDE和主要功能
- **开源声明**：开源免费声明
- **警告信息**：防诈骗警告
- **项目链接**：GitHub仓库链接
- **可选设置**：不再显示选项

### 5. components.py - 通用组件

#### ModernButton 类
```python
class ModernButton(QPushButton):
    """现代化按钮组件 - 替代CursorProButton"""
    
    def __init__(self, text: str, button_type: str = "primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self._setup_button()
```

#### 组件类型
- **ModernButton**：现代化按钮，支持多种样式
- **TitleLabel**：标题标签
- **SubtitleLabel**：副标题标签
- **SecondaryLabel**：次要文本标签
- **LinkLabel**：可点击链接标签
- **WarningFrame**：警告信息框
- **SectionFrame**：区域框架
- **ScrollableFrame**：可滚动框架
- **StatusLabel**：状态显示标签

### 6. styles.py - 样式系统

#### CursorPro 颜色系统
```python
COLORS = {
    'primary': '#4f46e5',      # 主色调
    'primary_hover': '#4338ca', # 主色调悬停
    'secondary': '#6b7280',     # 次要颜色
    'warning': '#dc2626',       # 警告色
    'success': '#059669',       # 成功色
    'background': '#f5f5f5',    # 背景色
    'surface': '#ffffff',       # 表面色
    'text_primary': '#1f2937',  # 主要文字
    'text_secondary': '#6b7280', # 次要文字
}
```

#### 样式函数
- **get_button_style()**：按钮样式
- **get_combobox_style()**：下拉框样式
- **get_label_style()**：标签样式
- **get_textedit_style()**：文本编辑器样式
- **get_complete_style()**：完整应用样式

### 7. font_manager.py - 字体管理

#### FontManager 类
```python
class FontManager:
    """跨平台字体管理器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        
        # 系统字体映射
        self.system_fonts = {
            'windows': {
                'default': ['Microsoft YaHei', 'SimHei', 'Arial'],
                'monospace': ['Consolas', 'Courier New']
            },
            'darwin': {  # macOS
                'default': ['PingFang SC', 'Helvetica Neue', 'SF Pro Display'],
                'monospace': ['SF Mono', 'Monaco', 'Menlo']
            },
            'linux': {
                'default': ['Noto Sans CJK SC', 'DejaVu Sans', 'Ubuntu'],
                'monospace': ['Ubuntu Mono', 'DejaVu Sans Mono']
            }
        }
```

#### 字体功能
- **跨平台兼容**：自动选择最佳可用字体
- **字体检测**：检查系统可用字体
- **缓存机制**：缓存选中的字体
- **便捷接口**：提供简单的字体获取函数

### 8. workers.py - 工作线程

#### BaseWorker 类
```python
class BaseWorker(QThread):
    """基础工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度更新
    status_changed = pyqtSignal(str, str)  # 状态变化
    task_completed = pyqtSignal(bool)  # 任务完成
```

#### 工作线程类型
- **CloseIDEWorker**：关闭IDE工作线程
- **CleanDatabaseWorker**：清理数据库工作线程
- **ModifyIDsWorker**：修改遥测ID工作线程
- **RunAllWorker**：一键执行所有工具工作线程

## 设计模式和架构

### 1. MVC 架构
- **Model**：配置管理器、语言管理器
- **View**：各种页面和对话框
- **Controller**：主窗口和事件处理

### 2. 信号-槽机制
```python
# 信号定义
continue_clicked = pyqtSignal()

# 信号连接
self.continue_btn.clicked.connect(self._on_continue)

# 信号发射
self.continue_clicked.emit()
```

### 3. 组件化设计
- **可重用组件**：通用UI组件
- **样式分离**：独立的样式系统
- **字体管理**：统一的字体管理

### 4. 线程安全
- **工作线程**：耗时操作在后台线程执行
- **信号通信**：线程间通过信号通信
- **UI更新**：只在主线程更新UI

## 国际化支持

### 1. 文本本地化
```python
# 获取本地化文本
title = get_text("app.title")
welcome = get_text("app.welcome")

# 参数化文本
message = get_text("dialogs.messages.close_warning", ide_name="VS Code")
```

### 2. 动态语言切换
```python
def _on_language_change(self, selected_display: str):
    """处理语言变更"""
    # 设置新语言
    self.language_manager.set_language(code)
    # 更新UI文本
    self._update_ui_texts()
```

### 3. 布局适应
- 自动调整组件大小以适应不同语言的文本长度
- 支持从左到右和从右到左的文本方向
- 字体选择考虑语言特性

## 用户体验优化

### 1. 响应式设计
- 固定窗口大小确保一致的用户体验
- 组件自动调整以适应内容
- 滚动区域处理长内容

### 2. 视觉反馈
- 按钮状态变化（悬停、按下、禁用）
- 进度指示和状态更新
- 彩色消息区分不同类型的信息

### 3. 交互优化
- 键盘快捷键支持
- 鼠标悬停提示
- 确认对话框防止误操作

## 错误处理和调试

### 1. 异常处理
```python
try:
    # 执行操作
    result = some_operation()
except Exception as e:
    # 记录错误
    logger.error(f"Operation failed: {e}")
    # 显示用户友好的错误信息
    self.show_error_message(str(e))
```

### 2. 日志系统
- 详细的操作日志
- 错误堆栈跟踪
- 用户操作记录

### 3. 调试支持
- 开发模式支持
- 调试信息输出
- 性能监控

## 性能优化

### 1. 懒加载
- 页面按需创建
- 资源延迟加载
- 组件缓存机制

### 2. 内存管理
- 及时释放不需要的对象
- 避免内存泄漏
- 合理使用缓存

### 3. 渲染优化
- 减少不必要的重绘
- 优化样式表
- 使用硬件加速

## 扩展和定制

### 1. 添加新页面
1. 创建新的页面类继承QWidget
2. 实现UI布局和事件处理
3. 在主窗口中添加页面管理
4. 更新导航逻辑

### 2. 自定义组件
1. 继承现有的Qt组件
2. 实现自定义的绘制和行为
3. 添加到components.py模块
4. 更新样式表

### 3. 主题系统
1. 扩展颜色定义
2. 创建主题切换机制
3. 支持用户自定义主题
4. 保存主题偏好

## 使用示例

### 启动应用程序
```python
from gui_qt6.main_window import main

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
```

### 创建自定义组件
```python
from gui_qt6.components import ModernButton

# 创建按钮
button = ModernButton("点击我", "primary")
button.clicked.connect(self.on_button_clicked)
```

### 使用工作线程
```python
from gui_qt6.workers import CleanDatabaseWorker

# 创建工作线程
worker = CleanDatabaseWorker(IDEType.VSCODE, "augment")
worker.progress_updated.connect(self.update_progress)
worker.task_completed.connect(self.on_task_complete)
worker.start()
```

## 部署和分发

### 1. 依赖要求
- Python 3.7+
- PyQt6 >= 6.4.0
- 其他项目依赖

### 2. 打包选项
- PyInstaller 可执行文件
- Python wheel 包
- 便携式压缩包

### 3. 平台兼容性
- Windows 7/10/11
- macOS 10.14+
- Linux (主要发行版)

这个GUI模块提供了完整的现代化用户界面，具有良好的用户体验、跨平台兼容性和可扩展性。