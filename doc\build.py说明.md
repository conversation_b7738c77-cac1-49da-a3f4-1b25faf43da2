# build.py 代码说明文档

## 文件概述
`build.py` 是 AugmentCode-Free 的专业级构建系统，提供完整的生产级构建自动化功能。它支持多格式包构建、跨平台可执行文件生成、完整的校验和生成以及自动化发布流程。

## 主要功能

### 1. 构建系统特性
- **多格式包构建**：wheel、sdist、exe、portable
- **跨平台可执行文件生成**：使用 PyInstaller
- **完整校验和生成和验证**：SHA256、SHA1、MD5
- **自动化发布说明和文档**
- **并行构建处理**（在可能的情况下）
- **完整的错误处理和恢复**
- **构建产物验证和测试**

### 2. 构建配置
```python
# 构建配置
VERSION = "1.0.6"
PROJECT_NAME = "AugmentCode-Free"
AUTHOR = "BasicProtein"
DESCRIPTION = "多IDE维护工具包 - 支持VS Code、Cursor、Windsurf、JetBrains"
GITHUB_REPO = f"https://github.com/{AUTHOR}/{PROJECT_NAME}"
BUILD_TIMESTAMP = datetime.now().isoformat()

# 构建环境配置
BUILD_CONFIG = {
    "python_min_version": (3, 7),
    "build_timeout": 600,
    "parallel_builds": True,
    "compression_level": 9,
    "create_checksums": True,
    "validate_builds": True,
    "cleanup_temp": True
}
```

## 核心类和组件

### 1. BuildLogger 类
```python
class BuildLogger:
    """增强的构建过程日志系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.step_counter = 0
        self.start_time = time.time()
    
    def step(self, step_name: str) -> None:
        """记录构建步骤"""
        self.step_counter += 1
        elapsed = time.time() - self.start_time
        message = f"STEP {self.step_counter}: {step_name} (Elapsed: {elapsed:.2f}s)"
```

#### 日志功能
- **步骤跟踪**：自动编号的构建步骤
- **时间统计**：每个步骤的执行时间
- **彩色输出**：不同类型消息的颜色区分
- **文件记录**：同时输出到控制台和日志文件

### 2. BuildEnvironment 类
```python
class BuildEnvironment:
    """构建环境管理和验证"""
    
    def __init__(self):
        self.python_version = sys.version_info
        self.platform_info = {
            'system': platform.system(),
            'machine': platform.machine(),
            'platform': platform.platform(),
            'python_version': platform.python_version()
        }
```

#### 环境验证功能
- **Python版本检查**：确保满足最低版本要求
- **平台信息收集**：系统、架构、Python版本
- **必需文件检查**：验证构建所需的文件存在
- **构建目录管理**：创建和清理构建目录

### 3. DependencyManager 类
```python
class DependencyManager:
    """综合依赖管理"""

    def __init__(self):
        self.build_dependencies = {
            'core': ['build>=0.10.0', 'wheel>=0.40.0', 'setuptools>=68.0.0'],
            'packaging': ['pyinstaller>=5.13.0', 'twine>=4.0.0']
        }
```

#### 依赖管理功能
- **自动依赖安装**：安装构建所需的所有依赖
- **版本验证**：确保依赖版本满足要求
- **分类管理**：核心依赖和打包依赖分别管理
- **安装验证**：验证依赖是否正确安装

## 构建组件

### 1. PythonPackageBuilder 类
```python
class PythonPackageBuilder:
    """专业Python包构建"""

    def build_python_packages(self) -> bool:
        """构建wheel和源码分发包"""
        try:
            # 构建wheel和sdist
            run_command("python -m build --wheel --sdist --outdir dist", timeout=300)
            
            # 验证包是否创建成功
            wheel_files = list(self.build_env.dist_dir.glob("*.whl"))
            sdist_files = list(self.build_env.dist_dir.glob("*.tar.gz"))
```

#### 包构建功能
- **Wheel包构建**：创建二进制分发包
- **源码包构建**：创建源码分发包
- **构建验证**：确保包文件正确生成
- **大小统计**：记录生成包的大小信息

### 2. ExecutableBuilder 类
```python
class ExecutableBuilder:
    """Windows可执行文件构建（使用PyInstaller）"""

    def create_pyinstaller_spec(self) -> str:
        """创建综合的PyInstaller规格文件"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# {PROJECT_NAME} v{VERSION} PyInstaller Specification
# Generated: {BUILD_TIMESTAMP}

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('augment_tools_core', 'augment_tools_core'),
        ('gui_qt6', 'gui_qt6'),
        ('languages', 'languages'),
        ('config', 'config'),
        ('README.md', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'PyQt6', 'PyQt6.QtWidgets', 'PyQt6.QtCore', 'PyQt6.QtGui',
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
        'click', 'colorama', 'pathlib', 'sqlite3', 'json', 'uuid',
        'platform', 'subprocess', 'threading', 'queue', 'time', 'psutil',
        'xml.etree.ElementTree', 'shutil', 'tempfile'
    ],
    excludes=['matplotlib', 'numpy', 'pandas', 'scipy', 'PIL'],
)'''
```

#### 可执行文件构建功能
- **自动规格文件生成**：创建详细的PyInstaller配置
- **依赖包含**：自动包含所有必需的模块和数据文件
- **隐藏导入处理**：处理动态导入的模块
- **排除不需要的包**：减小可执行文件大小

### 3. PortablePackageBuilder 类
```python
class PortablePackageBuilder:
    """跨平台便携包创建"""

    def create_portable_package(self) -> bool:
        """创建综合便携包"""
        portable_dir = self.build_env.build_dir / f"{PROJECT_NAME}-v{VERSION}-Portable"
        
        try:
            # 创建便携目录
            if portable_dir.exists():
                shutil.rmtree(portable_dir)
            portable_dir.mkdir()
            
            # 复制核心文件
            core_files = ['main.py', 'gui.py', 'run_gui.py', 'setup.py',
                         'requirements.txt', 'README.md', '.gitignore']
```

#### 便携包功能
- **跨平台支持**：Windows、Linux、macOS
- **启动脚本生成**：为不同平台创建启动脚本
- **文档生成**：创建使用说明和安装指南
- **压缩打包**：创建ZIP格式的便携包

## 高级功能

### 1. ChecksumGenerator 类
```python
class ChecksumGenerator:
    """综合校验和生成和验证"""

    def generate_checksums(self) -> bool:
        """为所有构建产物生成综合校验和"""
        checksums = {}

        # 处理所有构建产物
        for file_path in self.build_env.dist_dir.iterdir():
            if file_path.is_file() and not file_path.name.startswith('checksums'):
                try:
                    with open(file_path, 'rb') as f:
                        content = f.read()

                    checksums[file_path.name] = {
                        'size': len(content),
                        'sha256': hashlib.sha256(content).hexdigest(),
                        'md5': hashlib.md5(content).hexdigest(),
                        'sha1': hashlib.sha1(content).hexdigest()
                    }
```

#### 校验和功能
- **多种哈希算法**：SHA256、SHA1、MD5
- **文件大小记录**：记录每个文件的大小
- **标准格式输出**：生成标准的校验和文件
- **完整性验证**：支持构建产物的完整性验证

### 2. ReleaseNotesGenerator 类
```python
class ReleaseNotesGenerator:
    """生成综合发布说明"""

    def generate_release_notes(self) -> bool:
        """生成详细的发布说明"""
        release_notes = f"""# {PROJECT_NAME} v{VERSION} Release

## Overview
{DESCRIPTION}

## New Features
- **Multi-IDE Support**: Complete support for VS Code, Cursor, Windsurf, and JetBrains
- **Advanced Database Cleaning**: Intelligent cleanup of IDE-specific database entries
- **Telemetry Management**: Comprehensive telemetry ID modification and reset
```

#### 发布说明功能
- **自动版本信息**：包含版本号和构建时间
- **功能描述**：详细的新功能和改进说明
- **下载选项**：不同格式包的下载说明
- **安装指南**：详细的安装和使用说明

## 命令执行系统

### run_command 函数
```python
def run_command(cmd: str, cwd: Optional[str] = None, timeout: int = 300, 
                capture_output: bool = True, check: bool = True) -> subprocess.CompletedProcess:
    """执行命令并进行综合错误处理"""
    logger.info(f"Executing: {cmd}")
    if cwd:
        logger.info(f"Working directory: {cwd}")
    
    try:
        result = subprocess.run(
            cmd, shell=True, cwd=cwd, capture_output=capture_output,
            text=True, timeout=timeout, encoding='utf-8', errors='replace'
        )
```

#### 命令执行功能
- **超时控制**：防止命令执行时间过长
- **输出捕获**：捕获命令的标准输出和错误输出
- **错误处理**：详细的错误信息和恢复建议
- **编码处理**：正确处理不同编码的输出

## 文本清理系统

### clean_text 函数
```python
def clean_text(text: str) -> str:
    """清理文本中的特殊字符，确保可以安全输出"""
    if not text:
        return ""

    # 移除或替换问题字符
    text = text.replace('\ufffd', '?')  # 替换替换字符
    text = text.replace('\x00', '')    # 移除空字符

    # 确保可以编码为当前系统编码
    try:
        text.encode('utf-8')
        return text
    except UnicodeEncodeError:
        return text.encode('utf-8', errors='replace').decode('utf-8')
```

#### 文本处理功能
- **特殊字符处理**：移除或替换问题字符
- **编码安全**：确保文本可以安全输出
- **跨平台兼容**：处理不同系统的编码问题

## 完整构建系统

### CompleteBuildSystem 类
```python
class CompleteBuildSystem:
    """主构建系统协调器"""

    def __init__(self):
        self.build_env = BuildEnvironment()
        self.dependency_manager = DependencyManager()
        self.python_builder = PythonPackageBuilder(self.build_env)
        self.exe_builder = ExecutableBuilder(self.build_env)
        self.portable_builder = PortablePackageBuilder(self.build_env)
        self.checksum_generator = ChecksumGenerator(self.build_env)
        self.release_generator = ReleaseNotesGenerator(self.build_env)
```

#### 构建流程
1. **环境验证**：检查构建环境和依赖
2. **清理构建产物**：清理之前的构建结果
3. **安装依赖**：安装构建所需的依赖包
4. **Python包构建**：构建wheel和源码包
5. **可执行文件构建**：创建Windows可执行文件
6. **便携包构建**：创建跨平台便携包
7. **校验和生成**：为所有产物生成校验和
8. **发布说明生成**：创建详细的发布文档

## 使用方法

### 直接运行
```bash
python build.py
```

### 构建特定组件
```python
# 只构建Python包
builder = CompleteBuildSystem()
builder.build_python_packages_only()

# 只构建可执行文件
builder.build_executable_only()

# 完整构建
builder.run_complete_build()
```

## 输出产物

### 构建产物类型
1. **Python包**：
   - `augment_tools_core-1.0.6-py3-none-any.whl`
   - `augment-tools-core-1.0.6.tar.gz`

2. **Windows可执行文件**：
   - `AugmentCode-Free-v1.0.6.exe`

3. **便携包**：
   - `AugmentCode-Free-v1.0.6-Portable.zip`

4. **校验和文件**：
   - `checksums.txt`
   - `SHA256SUMS`

5. **文档**：
   - `RELEASE_NOTES.md`

## 最佳实践

### 1. 构建环境
- 使用干净的Python环境
- 确保所有依赖都是最新版本
- 在构建前清理之前的产物

### 2. 错误处理
- 每个构建步骤都有详细的错误处理
- 提供具体的错误信息和解决建议
- 支持部分构建失败后的恢复

### 3. 质量保证
- 自动验证构建产物的完整性
- 生成详细的构建日志
- 提供校验和用于完整性验证

## 注意事项

1. **系统要求**：Python 3.7+，足够的磁盘空间
2. **网络连接**：需要网络连接来下载依赖
3. **权限要求**：需要写入权限来创建构建产物
4. **平台限制**：Windows可执行文件只能在Windows上构建
5. **内存使用**：大型项目构建可能需要较多内存

这个构建系统提供了完整的自动化构建流程，确保生成高质量、可验证的发布产物。